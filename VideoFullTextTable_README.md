# VideoFullText Table and API

## Overview

I've created a new table and repository system for storing complete video transcript text that can be used for efficient phrase searching. This complements the existing word-by-word timing table.

## Database Schema

### New Table: `VideoFullTexts`
- `Id` (Primary Key)
- `VideoId` (Foreign Key to Videos table, Unique)
- `FullText` (Complete transcript text)
- `CreatedAt` (Timestamp)
- `UpdatedAt` (Timestamp)

### Relationship
- One-to-One relationship with the `Videos` table
- Cascade delete when video is deleted
- Unique index on `VideoId` for performance

## API Endpoints

### Search Endpoints
- `GET /api/video-full-text/search` - Search for phrases in video transcripts
  - Query parameters: `searchText`, `matchWholeWord`, `matchCase`, `pageNumber`, `pageSize`
  - Returns paginated results with context before/after matches

### CRUD Endpoints
- `GET /api/video-full-text/{id}` - Get VideoFullText by ID
- `GET /api/video-full-text/by-video/{videoId}` - Get VideoFullText by Video ID
- `POST /api/video-full-text` - Create new VideoFullText
- `PUT /api/video-full-text/{id}` - Update VideoFullText by ID
- `PUT /api/video-full-text/by-video/{videoId}` - Update VideoFullText by Video ID
- `POST /api/video-full-text/create-or-update/{videoId}` - Create or update VideoFullText for a video
- `DELETE /api/video-full-text/{id}` - Delete VideoFullText by ID
- `DELETE /api/video-full-text/by-video/{videoId}` - Delete VideoFullText by Video ID

## Automatic Integration

The VideoFullText records are automatically managed when videos are created, updated, or deleted:

1. **Video Creation**: When a video is created with a transcript, a VideoFullText record is automatically created
2. **Video Update**: When a video is updated, the corresponding VideoFullText record is updated
3. **Video Deletion**: When a video is deleted, the VideoFullText record is automatically deleted (cascade)

## Frontend Components

### VideoFullTextSearch Component
A React component for searching video transcripts with:
- Text input for search phrases
- Options for whole word matching and case sensitivity
- Paginated results with context highlighting
- Integration with video selection

### API Client
TypeScript API client (`videoFullTextApi.ts`) with functions for all endpoints.

## Usage Examples

### Backend (C#)
```csharp
// Search for phrases
var searchRequest = new VideoFullTextSearchRequest
{
    SearchText = "machine learning",
    MatchWholeWord = true,
    PageNumber = 1,
    PageSize = 10
};
var results = await _videoFullTextService.SearchAsync(searchRequest);

// Create or update full text for a video
await _videoFullTextService.CreateOrUpdateForVideoAsync(videoId, fullTranscriptText);
```

### Frontend (TypeScript/React)
```typescript
// Search for phrases
const results = await searchVideoFullText({
    searchText: "machine learning",
    matchWholeWord: true,
    pageNumber: 1,
    pageSize: 10
});

// Create or update full text
await createOrUpdateVideoFullText(videoId, { fullText: transcriptText });
```

## Benefits

1. **Efficient Phrase Search**: Optimized for searching complete phrases across entire video transcripts
2. **Context Preservation**: Returns surrounding context for better understanding of matches
3. **Flexible Matching**: Supports whole word and case-sensitive matching options
4. **Automatic Management**: Seamlessly integrated with existing video CRUD operations
5. **Separation of Concerns**: Dedicated table for phrase searching while preserving word-timing data for precise navigation

## Relationship with Existing Tables

- **Videos Table**: Contains basic video metadata and the original transcript field
- **WordTimings Table**: Contains precise word-by-word timing data for navigation
- **VideoFullTexts Table**: Contains complete text for efficient phrase searching

This architecture allows you to:
- Use WordTimings for precise time-based navigation to specific words
- Use VideoFullTexts for fast phrase searching across entire transcripts
- Maintain data consistency through foreign key relationships
