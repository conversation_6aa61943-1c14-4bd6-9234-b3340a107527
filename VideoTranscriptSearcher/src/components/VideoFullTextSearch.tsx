import React, { useState } from 'react';
import { FiSearch, <PERSON>Loader, FiPlay, FiAlertCircle } from 'react-icons/fi';
import { searchVideoFullText, VideoFullTextSearchResult, VideoFullTextSearchRequest } from '../api/videoFullTextApi';

interface VideoFullTextSearchProps {
  onVideoSelect?: (videoId: number) => void;
}

const VideoFullTextSearch: React.FC<VideoFullTextSearchProps> = ({ onVideoSelect }) => {
  const [searchText, setSearchText] = useState('');
  const [matchWholeWord, setMatchWholeWord] = useState(false);
  const [matchCase, setMatchCase] = useState(false);
  const [results, setResults] = useState<VideoFullTextSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);

  const handleSearch = async (page: number = 1) => {
    if (!searchText.trim()) {
      setError('Please enter search text');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const request: VideoFullTextSearchRequest = {
        searchText: searchText.trim(),
        matchWholeWord,
        matchCase,
        pageNumber: page,
        pageSize: 10
      };

      const response = await searchVideoFullText(request);
      setResults(response.items);
      setCurrentPage(response.pageNumber);
      setTotalPages(Math.ceil(response.totalCount / 10));
      setTotalCount(response.totalCount);
    } catch (err) {
      setError('Failed to search video transcripts. Please try again.');
      console.error('Search error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      handleSearch(page);
    }
  };

  const highlightMatch = (text: string, match: string, matchCase: boolean) => {
    if (!match) return text;
    
    const flags = matchCase ? 'g' : 'gi';
    const regex = new RegExp(`(${match.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, flags);
    
    return text.split(regex).map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">
        Search Video Transcripts (Phrase Search)
      </h2>

      {/* Search Form */}
      <div className="space-y-4 mb-6">
        <div className="flex gap-2">
          <div className="flex-1">
            <input
              type="text"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              placeholder="Enter phrase to search for..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <button
            onClick={() => handleSearch()}
            disabled={loading || !searchText.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {loading ? <FiLoader className="animate-spin" /> : <FiSearch />}
            Search
          </button>
        </div>

        {/* Search Options */}
        <div className="flex gap-4 text-sm">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={matchWholeWord}
              onChange={(e) => setMatchWholeWord(e.target.checked)}
              className="rounded"
            />
            Match whole word
          </label>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={matchCase}
              onChange={(e) => setMatchCase(e.target.checked)}
              className="rounded"
            />
            Match case
          </label>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2 text-red-700">
          <FiAlertCircle />
          {error}
        </div>
      )}

      {/* Results Summary */}
      {totalCount > 0 && (
        <div className="mb-4 text-sm text-gray-600">
          Found {totalCount} match{totalCount !== 1 ? 'es' : ''} 
          {totalPages > 1 && ` (Page ${currentPage} of ${totalPages})`}
        </div>
      )}

      {/* Search Results */}
      <div className="space-y-4">
        {results.map((result, index) => (
          <div key={`${result.videoId}-${result.matchPosition}-${index}`} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
            <div className="flex items-start justify-between mb-2">
              <h3 className="font-medium text-gray-900">{result.videoTitle}</h3>
              {onVideoSelect && (
                <button
                  onClick={() => onVideoSelect(result.videoId)}
                  className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
                >
                  <FiPlay size={12} />
                  Open Video
                </button>
              )}
            </div>
            
            <div className="text-sm text-gray-700 leading-relaxed">
              <span className="text-gray-500">{result.contextBefore}</span>
              <span className="bg-yellow-200 px-1 rounded font-medium">
                {result.matchedText}
              </span>
              <span className="text-gray-500">{result.contextAfter}</span>
            </div>
            
            <div className="mt-2 text-xs text-gray-500">
              Match position: {result.matchPosition}
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-6 flex justify-center gap-2">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage <= 1}
            className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const page = Math.max(1, currentPage - 2) + i;
            if (page > totalPages) return null;
            
            return (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-1 text-sm border rounded-md ${
                  page === currentPage
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            );
          })}
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
            className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      )}

      {/* No Results */}
      {!loading && searchText && results.length === 0 && !error && (
        <div className="text-center py-8 text-gray-500">
          No matches found for "{searchText}"
        </div>
      )}
    </div>
  );
};

export default VideoFullTextSearch;
