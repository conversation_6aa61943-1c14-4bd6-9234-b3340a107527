import apiClient from './apiClient';

export interface VideoFullTextDto {
  id: number;
  videoId: number;
  fullText: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateVideoFullTextRequest {
  videoId: number;
  fullText: string;
}

export interface UpdateVideoFullTextRequest {
  fullText: string;
}

export interface VideoFullTextSearchRequest {
  searchText: string;
  matchWholeWord?: boolean;
  matchCase?: boolean;
  pageNumber?: number;
  pageSize?: number;
}

export interface VideoFullTextSearchResult {
  videoId: number;
  videoTitle: string;
  matchedText: string;
  matchPosition: number;
  contextBefore: string;
  contextAfter: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pageNumber: number;
  totalPages: number;
  totalCount: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

// Search video full text
export const searchVideoFullText = async (params: VideoFullTextSearchRequest): Promise<PaginatedResponse<VideoFullTextSearchResult>> => {
  const response = await apiClient.get('/video-full-text/search', { params });
  return response.data;
};

// Get video full text by ID
export const getVideoFullText = async (id: number): Promise<VideoFullTextDto> => {
  const response = await apiClient.get(`/video-full-text/${id}`);
  return response.data;
};

// Get video full text by video ID
export const getVideoFullTextByVideoId = async (videoId: number): Promise<VideoFullTextDto> => {
  const response = await apiClient.get(`/video-full-text/by-video/${videoId}`);
  return response.data;
};

// Create video full text
export const createVideoFullText = async (request: CreateVideoFullTextRequest): Promise<VideoFullTextDto> => {
  const response = await apiClient.post('/video-full-text', request);
  return response.data;
};

// Update video full text by ID
export const updateVideoFullText = async (id: number, request: UpdateVideoFullTextRequest): Promise<void> => {
  await apiClient.put(`/video-full-text/${id}`, request);
};

// Update video full text by video ID
export const updateVideoFullTextByVideoId = async (videoId: number, request: UpdateVideoFullTextRequest): Promise<void> => {
  await apiClient.put(`/video-full-text/by-video/${videoId}`, request);
};

// Create or update video full text for a video
export const createOrUpdateVideoFullText = async (videoId: number, request: UpdateVideoFullTextRequest): Promise<VideoFullTextDto> => {
  const response = await apiClient.post(`/video-full-text/create-or-update/${videoId}`, request);
  return response.data;
};

// Delete video full text by ID
export const deleteVideoFullText = async (id: number): Promise<void> => {
  await apiClient.delete(`/video-full-text/${id}`);
};

// Delete video full text by video ID
export const deleteVideoFullTextByVideoId = async (videoId: number): Promise<void> => {
  await apiClient.delete(`/video-full-text/by-video/${videoId}`);
};
