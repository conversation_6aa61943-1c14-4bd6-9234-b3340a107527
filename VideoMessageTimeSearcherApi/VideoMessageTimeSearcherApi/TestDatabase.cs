using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using VideoMessageTimeSearcherApi.DbContexts;

namespace VideoMessageTimeSearcherApi;

public class TestDatabase
{
    public static async Task Main(string[] args)
    {
        // Build configuration
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json")
            .Build();

        // Get connection string
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        Console.WriteLine($"Using connection string: {connectionString}");

        // Create DbContext options
        var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
        optionsBuilder.UseNpgsql(connectionString);

        // Test database connection
        using var context = new AppDbContext(optionsBuilder.Options);
        
        try
        {
            Console.WriteLine("Testing database connection...");
            await context.Database.OpenConnectionAsync();
            Console.WriteLine("✅ Database connection successful!");
            
            await VerifyDatabase.CheckVideoFullTextTable(context);
            await VerifyDatabase.ListAllTables(context);
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Database connection failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
