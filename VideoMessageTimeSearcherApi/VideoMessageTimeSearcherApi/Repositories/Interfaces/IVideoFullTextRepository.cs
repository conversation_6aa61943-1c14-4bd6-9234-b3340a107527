using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;

namespace VideoMessageTimeSearcherApi.Repositories.Interfaces
{
    public interface IVideoFullTextRepository
    {
        // CRUD operations
        Task<VideoFullText> GetByIdAsync(int id);
        Task<VideoFullText> GetByVideoIdAsync(int videoId);
        Task<VideoFullText> CreateAsync(VideoFullText videoFullText);
        Task<bool> UpdateAsync(VideoFullText videoFullText);
        Task<bool> DeleteAsync(int id);
        Task<bool> DeleteByVideoIdAsync(int videoId);
        
        // Search operations
        Task<PagedResult<VideoFullTextSearchResult>> SearchAsync(VideoFullTextSearchRequest request);
        Task<bool> ExistsForVideoAsync(int videoId);
    }
}
