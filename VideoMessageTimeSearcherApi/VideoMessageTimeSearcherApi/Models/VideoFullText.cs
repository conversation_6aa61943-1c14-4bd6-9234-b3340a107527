using System;

namespace VideoMessageTimeSearcherApi.Models
{
    public class VideoFullText
    {
        public int Id { get; set; }
        public int VideoId { get; set; }
        public string FullText { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation property
        public Video Video { get; set; }
    }
}
