using VideoMessageTimeSearcherApi.DTOs;

namespace VideoMessageTimeSearcherApi.Services.Interfaces
{
    public interface IVideoFullTextService
    {
        // CRUD operations
        Task<VideoFullTextDto> GetByIdAsync(int id);
        Task<VideoFullTextDto> GetByVideoIdAsync(int videoId);
        Task<VideoFullTextDto> CreateAsync(CreateVideoFullTextRequest request);
        Task<bool> UpdateAsync(int id, UpdateVideoFullTextRequest request);
        Task<bool> UpdateByVideoIdAsync(int videoId, UpdateVideoFullTextRequest request);
        Task<bool> DeleteAsync(int id);
        Task<bool> DeleteByVideoIdAsync(int videoId);
        
        // Search operations
        Task<PagedResult<VideoFullTextSearchResult>> SearchAsync(VideoFullTextSearchRequest request);
        
        // Utility operations
        Task<bool> ExistsForVideoAsync(int videoId);
        Task<VideoFullTextDto> CreateOrUpdateForVideoAsync(int videoId, string fullText);
    }
}
