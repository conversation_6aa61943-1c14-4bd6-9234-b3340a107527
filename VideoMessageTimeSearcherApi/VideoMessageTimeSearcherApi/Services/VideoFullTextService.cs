using AutoMapper;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Repositories.Interfaces;
using VideoMessageTimeSearcherApi.Services.Interfaces;

namespace VideoMessageTimeSearcherApi.Services
{
    public class VideoFullTextService : IVideoFullTextService
    {
        private readonly IVideoFullTextRepository _videoFullTextRepository;
        private readonly IVideoRepository _videoRepository;
        private readonly IMapper _mapper;

        public VideoFullTextService(
            IVideoFullTextRepository videoFullTextRepository,
            IVideoRepository videoRepository,
            IMapper mapper)
        {
            _videoFullTextRepository = videoFullTextRepository;
            _videoRepository = videoRepository;
            _mapper = mapper;
        }

        public async Task<VideoFullTextDto> GetByIdAsync(int id)
        {
            var videoFullText = await _videoFullTextRepository.GetByIdAsync(id);
            return videoFullText == null ? null : _mapper.Map<VideoFullTextDto>(videoFullText);
        }

        public async Task<VideoFullTextDto> GetByVideoIdAsync(int videoId)
        {
            var videoFullText = await _videoFullTextRepository.GetByVideoIdAsync(videoId);
            return videoFullText == null ? null : _mapper.Map<VideoFullTextDto>(videoFullText);
        }

        public async Task<VideoFullTextDto> CreateAsync(CreateVideoFullTextRequest request)
        {
            // Validate that the video exists
            var video = await _videoRepository.GetVideoByIdAsync(request.VideoId);
            if (video == null)
            {
                throw new ArgumentException($"Video with ID {request.VideoId} not found.");
            }

            // Check if VideoFullText already exists for this video
            var existingVideoFullText = await _videoFullTextRepository.GetByVideoIdAsync(request.VideoId);
            if (existingVideoFullText != null)
            {
                throw new InvalidOperationException($"VideoFullText already exists for video ID {request.VideoId}.");
            }

            var videoFullText = _mapper.Map<VideoFullText>(request);
            var createdVideoFullText = await _videoFullTextRepository.CreateAsync(videoFullText);
            return _mapper.Map<VideoFullTextDto>(createdVideoFullText);
        }

        public async Task<bool> UpdateAsync(int id, UpdateVideoFullTextRequest request)
        {
            var videoFullText = await _videoFullTextRepository.GetByIdAsync(id);
            if (videoFullText == null) return false;

            _mapper.Map(request, videoFullText);
            return await _videoFullTextRepository.UpdateAsync(videoFullText);
        }

        public async Task<bool> UpdateByVideoIdAsync(int videoId, UpdateVideoFullTextRequest request)
        {
            var videoFullText = await _videoFullTextRepository.GetByVideoIdAsync(videoId);
            if (videoFullText == null) return false;

            _mapper.Map(request, videoFullText);
            return await _videoFullTextRepository.UpdateAsync(videoFullText);
        }

        public async Task<bool> DeleteAsync(int id)
        {
            return await _videoFullTextRepository.DeleteAsync(id);
        }

        public async Task<bool> DeleteByVideoIdAsync(int videoId)
        {
            return await _videoFullTextRepository.DeleteByVideoIdAsync(videoId);
        }

        public async Task<PagedResult<VideoFullTextSearchResult>> SearchAsync(VideoFullTextSearchRequest request)
        {
            // Validate request
            if (request.PageNumber < 1) request.PageNumber = 1;
            if (request.PageSize < 1 || request.PageSize > 100) request.PageSize = 10;

            if (string.IsNullOrWhiteSpace(request.SearchText))
            {
                return new PagedResult<VideoFullTextSearchResult>
                {
                    Items = new List<VideoFullTextSearchResult>(),
                    TotalCount = 0,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
            }

            // Trim and normalize the search text
            request.SearchText = request.SearchText.Trim();

            return await _videoFullTextRepository.SearchAsync(request);
        }

        public async Task<bool> ExistsForVideoAsync(int videoId)
        {
            return await _videoFullTextRepository.ExistsForVideoAsync(videoId);
        }

        public async Task<VideoFullTextDto> CreateOrUpdateForVideoAsync(int videoId, string fullText)
        {
            // Validate that the video exists
            var video = await _videoRepository.GetVideoByIdAsync(videoId);
            if (video == null)
            {
                throw new ArgumentException($"Video with ID {videoId} not found.");
            }

            var existingVideoFullText = await _videoFullTextRepository.GetByVideoIdAsync(videoId);
            
            if (existingVideoFullText != null)
            {
                // Update existing
                existingVideoFullText.FullText = fullText;
                await _videoFullTextRepository.UpdateAsync(existingVideoFullText);
                return _mapper.Map<VideoFullTextDto>(existingVideoFullText);
            }
            else
            {
                // Create new
                var videoFullText = new VideoFullText
                {
                    VideoId = videoId,
                    FullText = fullText
                };
                var createdVideoFullText = await _videoFullTextRepository.CreateAsync(videoFullText);
                return _mapper.Map<VideoFullTextDto>(createdVideoFullText);
            }
        }
    }
}
