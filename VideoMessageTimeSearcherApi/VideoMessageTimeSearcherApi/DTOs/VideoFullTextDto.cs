using System;

namespace VideoMessageTimeSearcherApi.DTOs
{
    public class VideoFullTextDto
    {
        public int Id { get; set; }
        public int VideoId { get; set; }
        public string FullText { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateVideoFullTextRequest
    {
        public int VideoId { get; set; }
        public string FullText { get; set; } = string.Empty;
    }

    public class UpdateVideoFullTextRequest
    {
        public string FullText { get; set; } = string.Empty;
    }

    public class VideoFullTextSearchRequest
    {
        public string SearchText { get; set; } = string.Empty;
        public bool MatchWholeWord { get; set; } = false;
        public bool MatchCase { get; set; } = false;
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }

    public class VideoFullTextSearchResult
    {
        public int VideoId { get; set; }
        public string VideoTitle { get; set; } = string.Empty;
        public string MatchedText { get; set; } = string.Empty;
        public int MatchPosition { get; set; }
        public string ContextBefore { get; set; } = string.Empty;
        public string ContextAfter { get; set; } = string.Empty;
    }
}
