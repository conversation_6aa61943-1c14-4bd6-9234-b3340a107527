using Microsoft.AspNetCore.Mvc;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Services.Interfaces;

namespace VideoMessageTimeSearcherApi.Controllers
{
    [ApiController]
    [Route("api/video-full-text")]
    public class VideoFullTextController : ControllerBase
    {
        private readonly IVideoFullTextService _videoFullTextService;
        private readonly ILogger<VideoFullTextController> _logger;

        public VideoFullTextController(
            IVideoFullTextService videoFullTextService,
            ILogger<VideoFullTextController> logger)
        {
            _videoFullTextService = videoFullTextService;
            _logger = logger;
        }

        [HttpGet("search")]
        public async Task<ActionResult<PagedResult<VideoFullTextSearchResult>>> SearchVideoFullText(
            [FromQuery] string searchText,
            [FromQuery] bool matchWholeWord = false,
            [FromQuery] bool matchCase = false,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchText))
                {
                    return BadRequest("Search text is required.");
                }

                var request = new VideoFullTextSearchRequest
                {
                    SearchText = searchText,
                    MatchWholeWord = matchWholeWord,
                    MatchCase = matchCase,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };

                var result = await _videoFullTextService.SearchAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching video full text");
                return StatusCode(500, "An error occurred while searching video full text.");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<VideoFullTextDto>> GetVideoFullText(int id)
        {
            try
            {
                var videoFullText = await _videoFullTextService.GetByIdAsync(id);
                if (videoFullText == null) return NotFound();
                return Ok(videoFullText);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting video full text with ID {id}");
                return StatusCode(500, "An error occurred while retrieving the video full text.");
            }
        }

        [HttpGet("by-video/{videoId}")]
        public async Task<ActionResult<VideoFullTextDto>> GetVideoFullTextByVideoId(int videoId)
        {
            try
            {
                var videoFullText = await _videoFullTextService.GetByVideoIdAsync(videoId);
                if (videoFullText == null) return NotFound();
                return Ok(videoFullText);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting video full text for video ID {videoId}");
                return StatusCode(500, "An error occurred while retrieving the video full text.");
            }
        }

        [HttpPost]
        public async Task<ActionResult<VideoFullTextDto>> CreateVideoFullText([FromBody] CreateVideoFullTextRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest("Request cannot be null");
                }

                var result = await _videoFullTextService.CreateAsync(request);
                return CreatedAtAction(nameof(GetVideoFullText), new { id = result.Id }, result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Validation error creating video full text");
                return BadRequest(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation creating video full text");
                return Conflict(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating video full text");
                return StatusCode(500, "An error occurred while creating the video full text.");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateVideoFullText(int id, [FromBody] UpdateVideoFullTextRequest request)
        {
            try
            {
                var success = await _videoFullTextService.UpdateAsync(id, request);
                if (!success) return NotFound();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating video full text with ID {id}");
                return StatusCode(500, "An error occurred while updating the video full text.");
            }
        }

        [HttpPut("by-video/{videoId}")]
        public async Task<IActionResult> UpdateVideoFullTextByVideoId(int videoId, [FromBody] UpdateVideoFullTextRequest request)
        {
            try
            {
                var success = await _videoFullTextService.UpdateByVideoIdAsync(videoId, request);
                if (!success) return NotFound();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating video full text for video ID {videoId}");
                return StatusCode(500, "An error occurred while updating the video full text.");
            }
        }

        [HttpPost("create-or-update/{videoId}")]
        public async Task<ActionResult<VideoFullTextDto>> CreateOrUpdateVideoFullText(int videoId, [FromBody] UpdateVideoFullTextRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest("Request cannot be null");
                }

                var result = await _videoFullTextService.CreateOrUpdateForVideoAsync(videoId, request.FullText);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Validation error creating or updating video full text");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating or updating video full text for video ID {videoId}");
                return StatusCode(500, "An error occurred while creating or updating the video full text.");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteVideoFullText(int id)
        {
            try
            {
                var success = await _videoFullTextService.DeleteAsync(id);
                if (!success) return NotFound();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting video full text with ID {id}");
                return StatusCode(500, "An error occurred while deleting the video full text.");
            }
        }

        [HttpDelete("by-video/{videoId}")]
        public async Task<IActionResult> DeleteVideoFullTextByVideoId(int videoId)
        {
            try
            {
                var success = await _videoFullTextService.DeleteByVideoIdAsync(videoId);
                if (!success) return NotFound();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting video full text for video ID {videoId}");
                return StatusCode(500, "An error occurred while deleting the video full text.");
            }
        }
    }
}
